<!DOCTYPE html>
<html lang="zh-CN" data-dpr="1">
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <meta
      name="viewport"
      content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no"
    />

    <title></title>
    <script>
      var Base64 = {
        decode(base64String) {
          const padding =
            base64String.length % 4 === 0 ? 0 : 4 - (base64String.length % 4);
          base64String += "=".repeat(padding);
          const binaryString = window.atob(base64String);
          const bytes = new Uint8Array(binaryString.length);
          for (let i = 0; i < binaryString.length; i++) {
            bytes[i] = binaryString.charCodeAt(i);
          }
          return new TextDecoder("utf-8").decode(bytes);
        },
      };

      window.global_title = "a";
      window.global_cdn = ".";
      window.global_channel_code = "test";
      window.global_op_server = "lgoooo.com:6443";
      window.global_op = window.global_op_server;
      window.global_op_key = "mgnzasel";
      window.global_down_time = 3000;
      console.log(window.global_op);
    </script>
  </head>
  <body></body>
  <!--加载-->
  <script>
    (function () {
      var script = document.createElement("script");
      script.src = window.global_cdn + "/cdn_domain.js";
      script.onload = function () {
        load_js_css(window.global_cdn + "/conf.js", "js");
      };
      document.body.appendChild(script);
    })();
  </script>
</html>
